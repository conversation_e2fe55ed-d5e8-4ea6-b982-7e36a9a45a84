#!/usr/bin/env node

/**
 * Test script for SpreadsheetsAgent functionality
 */

import { SpreadsheetsAgent } from '../src/services/SpreadsheetsAgent.js';
import logger from '../src/config/logger.js';

const TEST_USER_ID = process.argv[2] || '8689180e-397e-4960-9b03-3ec69d7a23bb';

async function testSpreadsheetsAgent() {
  console.log('🧪 Testing SpreadsheetsAgent');
  console.log('=' .repeat(40));
  console.log(`User ID: ${TEST_USER_ID}`);
  console.log('');

  try {
    // Create agent instance
    console.log('1. Creating SpreadsheetsAgent instance...');
    const agent = new SpreadsheetsAgent();
    console.log('✅ Agent created successfully');
    console.log(`   → Tools available: ${agent.tools.length}`);
    agent.tools.forEach((tool, index) => {
      console.log(`     ${index + 1}. ${tool.name} - ${tool.description}`);
    });
    console.log('');

    // Test list_sheets tool directly
    console.log('2. Testing list_sheets tool directly...');
    const listSheetsTool = agent.tools.find(tool => tool.name === 'list_sheets');
    if (!listSheetsTool) {
      console.log('❌ list_sheets tool not found');
      return;
    }

    try {
      console.log('   → Invoking tool with args:', { userId: TEST_USER_ID });
      const toolResult = await listSheetsTool.invoke({ userId: TEST_USER_ID });
      console.log('✅ list_sheets tool executed successfully');
      
      // Parse and display result
      try {
        const parsedResult = JSON.parse(toolResult);
        if (parsedResult.error) {
          console.log('❌ Tool returned error:');
          console.log(`   → ${parsedResult.error}`);
        } else if (parsedResult.sheets) {
          console.log(`✅ Successfully retrieved ${parsedResult.sheets.length} sheets`);
          parsedResult.sheets.slice(0, 3).forEach((sheet, index) => {
            console.log(`   ${index + 1}. ${sheet.name} (${sheet.id})`);
          });
        } else {
          console.log('⚠️  Unexpected result format');
          console.log(`   → Result: ${toolResult.substring(0, 200)}...`);
        }
      } catch (parseError) {
        console.log('⚠️  Could not parse tool result as JSON');
        console.log(`   → Raw result: ${toolResult.substring(0, 200)}...`);
      }
    } catch (toolError) {
      console.log('❌ Tool execution failed:');
      console.log(`   → Error: ${toolError.message}`);
      return;
    }
    console.log('');

    // Test full agent workflow
    console.log('3. Testing full agent workflow...');
    try {
      const agentResponse = await agent.processRequest(
        TEST_USER_ID, 
        'List all my Google Sheets', 
        { action: 'list_sheets' }
      );
      
      console.log('✅ Agent workflow completed');
      console.log(`   → Success: ${agentResponse.success}`);
      console.log(`   → Message length: ${agentResponse.message?.length || 0} characters`);
      
      if (agentResponse.success) {
        console.log('   → Response preview:');
        console.log(`     ${agentResponse.message.substring(0, 200)}...`);
      } else {
        console.log('❌ Agent workflow failed:');
        console.log(`   → Error: ${agentResponse.error}`);
        console.log(`   → Details: ${agentResponse.details}`);
      }
    } catch (agentError) {
      console.log('❌ Agent workflow failed:');
      console.log(`   → Error: ${agentError.message}`);
      return;
    }
    console.log('');

    console.log('🎉 SpreadsheetsAgent test completed successfully!');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testSpreadsheetsAgent()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
