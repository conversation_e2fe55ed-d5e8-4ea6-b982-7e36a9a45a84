/* TheInfini AI Admin Dashboard Styles */

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    '<PERSON>bunt<PERSON>', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0a0a;
  color: #ffffff;
  line-height: 1.6;
}

/* CSS Variables for Dark Theme */
:root {
  --primary-bg: #0a0a0a;
  --secondary-bg: #1a1a1a;
  --card-bg: #1e1e1e;
  --border-color: #333333;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #666666;
  --accent-color: #fcd469;
  --accent-hover: #f5c842;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
  --hover-bg: rgba(255, 255, 255, 0.05);
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Admin Layout */
.admin-layout {
  display: flex;
  height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  background-color: var(--secondary-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  min-height: 100vh;
  width: 280px;
}

.sidebar--collapsed {
  width: 60px;
}

.sidebar__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Sidebar Header */
.sidebar__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.sidebar__logo h2 {
  color: var(--accent-color);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar__logo-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  display: block;
  margin-top: 2px;
}

.sidebar--collapsed .sidebar__logo img,
.sidebar--collapsed .sidebar__logo-subtitle {
  display: none;
}

.sidebar__toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar__toggle i {
  font-size: 20px;
}
.sidebar__toggle:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* Sidebar Navigation */
.sidebar__nav {
  flex: 1;
  padding: 16px 8px;
}

.sidebar__nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar__nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.sidebar--collapsed .sidebar__nav-item {
  justify-content: center;
  padding: 12px;
}

.sidebar__nav-item:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.sidebar__nav-item--active {
  background-color: var(--card-bg);
  color: var(--accent-color);
}
.sidebar.sidebar--collapsed .sidebar__user-profile {
  flex-direction: column;
}
.sidebar__nav-icon {
  font-size: 18px;
  min-width: 20px;
  text-align: center;
}

.sidebar__nav-label {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar--collapsed .sidebar__nav-label {
  display: none;
}

.sidebar__nav-badge {
  background-color: var(--error-color);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  line-height: 1;
}

.sidebar--collapsed .sidebar__nav-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  min-width: 16px;
  height: 16px;
  font-size: 10px;
  padding: 1px 4px;
}

/* Sidebar Bottom */
.sidebar__bottom {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

.sidebar__user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: var(--card-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.sidebar__user-profile:hover {
  background-color: var(--hover-bg);
}

.sidebar--collapsed .sidebar__user-profile {
  justify-content: center;
  padding: 12px;
}

.sidebar__user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.sidebar__user-avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.sidebar__user-info {
  flex: 1;
  min-width: 0;
}

.sidebar--collapsed .sidebar__user-info {
  display: none;
}

.sidebar__user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__user-plan {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__logout-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
}

.sidebar__logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--primary-bg);
}

/* Top Bar */
.topbar {
  background-color: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 0 24px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.topbar__left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.topbar__menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.topbar__menu-btn:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.topbar__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.topbar__right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.topbar__user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.topbar__user-name {
  font-size: 14px;
  color: var(--text-secondary);
}

.topbar__user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
}

/* Content Area */
.content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-overlay.show {
  display: flex;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner p {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dashboard Styles */
.dashboard {
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}
.page-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-card__content {
  flex: 1;
}

.stat-card__value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-card__label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.dashboard-section {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  padding: 24px;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--hover-bg);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.activity-details {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: var(--text-muted);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  background-color: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.quick-action-btn:hover {
  background-color: var(--card-bg);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.quick-action-btn i {
  font-size: 24px;
}

.quick-action-btn span {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* System Stats */
.system-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.system-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.system-stat:last-child {
  border-bottom: none;
}

.system-stat__label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.system-stat__value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator--online {
  color: var(--success-color);
}

.status-indicator--offline {
  color: var(--error-color);
}

.status-indicator--warning {
  color: var(--warning-color);
}

.status-indicator i {
  font-size: 8px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
  font-style: italic;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.btn-primary {
  background-color: var(--accent-color);
  color: var(--primary-bg);
}

.btn-primary:hover {
  background-color: var(--accent-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Notification Styles */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.notification {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-medium);
  animation: slideIn 0.3s ease-out;
}

.notification--success {
  border-left: 4px solid var(--success-color);
}

.notification--error {
  border-left: 4px solid var(--error-color);
}

.notification--warning {
  border-left: 4px solid var(--warning-color);
}

.notification--info {
  border-left: 4px solid var(--info-color);
}

.notification__content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.notification__icon {
  font-size: 16px;
}

.notification--success .notification__icon {
  color: var(--success-color);
}

.notification--error .notification__icon {
  color: var(--error-color);
}

.notification--warning .notification__icon {
  color: var(--warning-color);
}

.notification--info .notification__icon {
  color: var(--info-color);
}

.notification__message {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.notification__close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.notification__close:hover {
  background-color: var(--hover-bg);
  color: var(--text-primary);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .topbar__menu-btn {
    display: flex;
  }

  .main-content {
    width: 100%;
  }

  .content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Subscription Action Cards */
.subscription-actions {
  padding: 20px 0;
}

.action-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 20px;
}

.action-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-color);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover::before {
  opacity: 1;
}

.action-icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.action-icon i {
  font-size: 24px;
  color: white;
}

/* Action Count Styles */
.action-count {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: white;
}

.count-number {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.count-number i {
  font-size: 16px;
}

.count-label {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  line-height: 1.2;
}

.action-content h4 {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.action-content p {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.action-btn.primary {
  background: var(--accent-color);
  color: #000;
}

.action-btn.primary:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.action-btn.secondary:hover {
  background: var(--hover-bg);
  border-color: var(--accent-color);
}

.action-btn.success {
  background: var(--success-color);
  color: white;
}

.action-btn.success:hover {
  background: #059669;
  transform: translateY(-1px);
}

.action-btn.danger {
  background: #dc2626;
  color: white;
}

.action-btn.danger:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.action-btn i {
  font-size: 12px;
}

/* Responsive adjustments for action cards */
@media (max-width: 768px) {
  .action-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    padding: 20px;
  }

  .action-icon {
    width: 50px;
    height: 50px;
  }

  .action-icon i {
    font-size: 20px;
  }
}

/* Paid Users Table Styles */
.users-table-container {
  overflow-x: auto;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
}

.users-table th,
.users-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.users-table th {
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.users-table tbody tr:hover {
  background: var(--hover-bg);
}

.users-table tbody tr:last-child td {
  border-bottom: none;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 250px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-avatar img,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.user-name .no-name {
  color: var(--text-muted);
  font-style: italic;
}

.user-email {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: 2px;
  word-break: break-all;
}

.user-mobile {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: 8px;
}

.user-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.badge.active {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.badge.inactive {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.badge.verified {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.badge.unverified {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

/* Subscription Info Styles */
.subscription-info {
  min-width: 150px;
}

.subscription-id {
  margin-bottom: 8px;
}

.subscription-id small {
  color: var(--text-muted);
  font-family: monospace;
}

.subscription-status {
  margin-bottom: 8px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.status-badge.expired {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-badge.suspended {
  background: rgba(156, 163, 175, 0.1);
  color: #9ca3af;
}

.status-badge i {
  font-size: 8px;
}

.cancellation-reason {
  margin-top: 8px;
}

.cancellation-reason small {
  color: var(--text-secondary);
  font-size: 11px;
  line-height: 1.3;
}

/* Plan Info Styles */
.plan-info {
  min-width: 180px;
}

.plan-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
}

.plan-type {
  margin-bottom: 6px;
}

.plan-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.plan-badge.explorer {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.plan-badge.creator {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.plan-badge.pro {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.plan-badge.addon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.plan-price {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: 6px;
}

.plan-credits {
  color: var(--text-secondary);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Dates Info Styles */
.dates-info {
  min-width: 140px;
}

.date-item {
  margin-bottom: 8px;
}

.date-item:last-child {
  margin-bottom: 0;
}

.date-item small {
  color: var(--text-muted);
  font-size: 11px;
  display: block;
  margin-bottom: 2px;
}

.date-item div {
  color: var(--text-secondary);
  font-size: 13px;
}

.user-joined {
  min-width: 120px;
}

.user-joined small {
  color: var(--text-muted);
  font-size: 11px;
  display: block;
  margin-bottom: 2px;
}

.user-joined div {
  color: var(--text-secondary);
  font-size: 13px;
}

/* Pagination Styles */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.pagination-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pagination-btn:hover {
  background: var(--hover-bg);
  border-color: var(--accent-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;
}

.pagination-info .total-count {
  font-size: 12px;
  color: var(--text-muted);
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.section-actions .pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Search Functionality Styles */
.search-container {
  flex: 1;
  max-width: 400px;
}

.search-form {
  width: 100%;
}

.search-input-group {
  display: flex;
  align-items: center;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.3s ease;
}

.search-input-group:focus-within {
  border-color: var(--accent-color);
}

.search-input {
  flex: 1;
  padding: 10px 12px;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-btn {
  padding: 10px 12px;
  background: var(--accent-color);
  border: none;
  color: #000;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover {
  background: var(--accent-hover);
}

.clear-search-btn {
  padding: 10px 12px;
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  border-left: 1px solid var(--border-color);
}

.clear-search-btn:hover {
  background: var(--hover-bg);
  color: var(--error-color);
}

.search-results {
  display: block;
  color: var(--accent-color);
  font-size: 11px;
  margin-top: 2px;
}

/* Responsive adjustments for tables */
@media (max-width: 1200px) {
  .users-table th,
  .users-table td {
    padding: 12px;
  }

  .user-info {
    min-width: 200px;
  }

  .subscription-info,
  .plan-info,
  .dates-info {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .users-table-container {
    font-size: 13px;
  }

  .users-table th,
  .users-table td {
    padding: 8px;
  }

  .user-info {
    min-width: 150px;
  }

  .user-avatar img,
  .avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .subscription-info,
  .plan-info,
  .dates-info,
  .user-joined {
    min-width: 100px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .section-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-container {
    max-width: none;
  }

  .search-input-group {
    flex-direction: row;
  }

  .search-input {
    min-width: 0;
  }
}

/* Add Subscription Page Styles */
.subscription-step {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.step-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.step-header h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border-radius: 50%;
  font-weight: 600;
  font-size: 1rem;
}

.step-header p {
  color: var(--text-secondary);
  margin-left: 2.5rem;
}

.input-group {
  display: flex;
  gap: 0.75rem;
  align-items: stretch;
}

.input-group input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1rem;
}

.input-group input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

.user-details {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: var(--secondary-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.user-card {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1.5rem;
  align-items: start;
}

.user-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.user-avatar {
  position: relative;
  width: 60px;
  height: 60px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-initials {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border-radius: 50%;
  font-weight: 600;
  font-size: 1.25rem;
}

.user-data h5 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.user-data p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.user-meta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-badge, .plan-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.active {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.status-badge.inactive {
  background: rgba(239, 68, 68, 0.2);
  color: var(--error-color);
}

.plan-badge.explorer {
  background: rgba(107, 114, 128, 0.2);
  color: #9ca3af;
}

.plan-badge.creator {
  background: rgba(59, 130, 246, 0.2);
  color: var(--info-color);
}

.plan-badge.pro {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.user-subscription h6 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.user-subscription p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.plan-card.selectable {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid var(--border-color);
  background: var(--card-bg);
  border-radius: 16px;
  box-shadow: var(--shadow-light);
}

.plan-card.selectable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--accent-color);
  background: rgba(252, 212, 105, 0.02);
}

.plan-card.selectable.selected {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.2);
  background: rgba(252, 212, 105, 0.05);
  transform: translateY(-2px);
}

.plan-card.selectable.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(107, 114, 128, 0.1);
  border-color: rgba(107, 114, 128, 0.3);
}

.plan-card.selectable.disabled:hover {
  transform: none;
  box-shadow: var(--shadow-light);
  border-color: rgba(107, 114, 128, 0.3);
  background: rgba(107, 114, 128, 0.1);
}

.plan-card.selectable.current-plan {
  border-color: var(--success-color);
  background: rgba(16, 185, 129, 0.05);
}

.current-plan-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--success-color);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  z-index: 10;
}

.current-plan-indicator i {
  font-size: 0.875rem;
}

.plan-select-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--success-color);
  color: white;
  padding: 0.5rem;
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.plan-card.selected .plan-select-indicator {
  opacity: 1;
  transform: scale(1);
}

.features-list {
  list-style: none;
  margin: 0.5rem 0;
}

.features-list li {
  padding: 0.25rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Enhanced Plans Grid for Add Subscription */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.plan-card {
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: var(--shadow-light);
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: rgba(252, 212, 105, 0.5);
}

.plan-header {
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.plan-type-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
}

.plan-type-badge.creator {
  background: rgba(59, 130, 246, 0.15);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.plan-type-badge.pro {
  background: rgba(139, 92, 246, 0.15);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.plan-type-badge.addon {
  background: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.plan-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.75rem 0;
}

.plan-price {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.plan-price .currency {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.plan-price .amount {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.plan-price .cycle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.plan-body {
  padding: 1rem 1.5rem 1.5rem;
}

.plan-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.plan-credits {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(252, 212, 105, 0.05);
  border: 1px solid rgba(252, 212, 105, 0.2);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.plan-credits.unlimited {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.plan-credits.limited {
  color: var(--accent-color);
}

.plan-credits i {
  font-size: 1rem;
}

.plan-features {
  margin-top: 1rem;
}

.plan-features h6 {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.plan-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
}

.review-card {
  background: var(--secondary-bg);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.review-section {
  margin-bottom: 1.5rem;
}

.review-section:last-child {
  margin-bottom: 0;
}

.review-section h5 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.125rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.review-item:last-child {
  border-bottom: none;
}

.review-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.review-item .value {
  color: var(--text-primary);
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.btn.large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  min-width: 160px;
}

.btn.success {
  background: var(--success-color);
  color: white;
  border: none;
}

.btn.success:hover {
  background: #059669;
}

.btn.danger {
  background: #dc2626;
  color: white;
}

.btn.danger:hover {
  background: #b91c1c;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  border: 1px solid var(--border-color);
}

.modal-content.success {
  border-color: var(--success-color);
}

.modal-header h4 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--success-color);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.modal-body {
  margin-bottom: 1.5rem;
}

.modal-body p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.success-details {
  background: var(--secondary-bg);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
}

.success-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.success-item:last-child {
  border-bottom: none;
}

.success-item strong {
  color: var(--text-secondary);
}

/* Warning Card Styles */
.warning-card {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.warning-card i {
  color: #f59e0b;
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.warning-content h6 {
  color: #92400e;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.warning-content ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #92400e;
}

.warning-content li {
  margin-bottom: 0.25rem;
}

/* Review Card Styles */
.review-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.review-card h6 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.review-item:last-child {
  border-bottom: none;
}

.review-item .label {
  font-weight: 500;
  color: var(--text-secondary);
}

.review-item .value {
  color: var(--text-primary);
  font-weight: 500;
}

/* User Info Card Styles */
.user-info-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.user-info h5 {
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.user-email {
  color: var(--text-secondary);
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
}

.user-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.meta-item i {
  font-size: 0.75rem;
}

.status-active {
  color: var(--success-color);
}

.status-inactive {
  color: var(--error-color);
}

/* Subscription Card Styles */
.subscriptions-grid {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.subscription-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.subscription-card.selectable:hover {
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subscription-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.subscription-header h6 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.125rem;
}

.subscription-details {
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.detail-item .value {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

.subscription-actions {
  display: flex;
}

.no-subscriptions {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.no-subscriptions i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--border-color);
}

.no-subscriptions p {
  margin: 0.5rem 0;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
  letter-spacing: 0.025em;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.95rem;
  font-family: inherit;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

/* Specific styling for cancellation reason textarea */
#cancellationReason {
  min-height: 80px;
  font-family: inherit;
  border-radius: 8px;
  transition: all 0.3s ease;
}

#cancellationReason:focus {
  min-height: 120px;
}

.input-group {
  display: flex;
  gap: 0.5rem;
  align-items: stretch;
}

.input-group input {
  flex: 1;
  margin-bottom: 0;
}

.input-group .btn {
  white-space: nowrap;
  margin-bottom: 0;
}

/* Search Form Styles */
.search-form {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
}

.search-form .form-group {
  margin-bottom: 0;
}

/* Cancellation Review Styles */
.cancellation-review {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
}

.cancellation-review .form-group {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

/* Dark mode form adjustments */
@media (prefers-color-scheme: dark) {
  .form-group input,
  .form-group textarea,
  .form-group select {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
  }

  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    border-color: var(--accent-color);
    background: var(--card-bg);
  }
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Duplicate loading overlay styles removed - using the original ones above */

.error-message {
  color: var(--error-color);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.page-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.back-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--secondary-bg);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.back-btn:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
  border-color: var(--accent-color);
}

/* Add Subscription Mobile Responsive Styles */
@media (max-width: 768px) {
  .user-card {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .input-group {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }

  .modal-footer {
    flex-direction: column;
  }

  .review-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .subscription-step {
    padding: 1rem;
  }

  .step-header h4 {
    font-size: 1.125rem;
  }

  .input-group {
    flex-direction: column;
  }

  .input-group .btn {
    margin-top: 0.5rem;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    width: 100%;
  }

  .user-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .search-form,
  .cancellation-review {
    padding: 1rem;
  }
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.875rem;
  }

  .btn.large {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    min-width: 140px;
  }


/* User Roles Specific Styles */
.user-search-section {
  margin-bottom: 24px;
}

.input-group {
  display: flex;
  align-items: stretch;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  transition: border-color 0.3s ease;
}

.input-group:focus-within {
  border-color: var(--accent-color);
}

.input-group input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.input-group input::placeholder {
  color: var(--text-muted);
}

.input-group .btn {
  border-radius: 0;
  border: none;
  border-left: 1px solid var(--border-color);
}

.user-details-section {
  margin-top: 24px;
}

.user-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar .avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
}

.user-details h4 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.user-details p {
  margin: 0 0 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.user-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.user-plan {
  background: var(--accent-color);
  color: #000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.user-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.user-status.active {
  background: #10b981;
  color: white;
}

.user-status.inactive {
  background: #ef4444;
  color: white;
}

.role-assignment-section {
  margin-top: 24px;
}

.role-assignment-section h4 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.roles-container {
  margin-bottom: 24px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.role-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.role-item:hover {
  border-color: var(--accent-color);
  background: var(--hover-bg);
}

.role-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  cursor: pointer;
  width: 100%;
  margin: 0;
}

.role-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.role-checkbox input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.role-checkbox input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000;
  font-weight: bold;
  font-size: 12px;
}

.role-info {
  flex: 1;
}

.role-info h5 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.role-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.4;
}

.roles-overview {
  margin-top: 16px;
}

.roles-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.role-overview-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.role-overview-card:hover {
  border-color: var(--accent-color);
  background: var(--hover-bg);
}

.role-overview-card h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.role-overview-card p {
  margin: 0 0 12px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.role-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.role-date {
  color: var(--text-muted);
  font-size: 12px;
}

/* Duplicate loading-overlay CSS removed - using the original one from earlier in the file */

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.modal-content.large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.modal-body {
  padding: 24px;
}

.form-help {
  display: block;
  margin-top: 4px;
  color: var(--text-muted);
  font-size: 12px;
  line-height: 1.4;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 400px;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.3s ease;
}

.notification-success {
  border-left: 4px solid #10b981;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

.notification-info {
  border-left: 4px solid var(--accent-color);
}

.notification i {
  font-size: 18px;
}

.notification-success i {
  color: #10b981;
}

.notification-error i {
  color: #ef4444;
}

.notification-info i {
  color: var(--accent-color);
}

.notification span {
  flex: 1;
  color: var(--text-primary);
  font-size: 14px;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.notification-close:hover {
  background: var(--hover-bg);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Support Module Styles */
.page-header {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

.page-header__content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}

.page-header__breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
}

.breadcrumb-link {
  color: var(--text-secondary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: color 0.2s ease;
}

.breadcrumb-link:hover {
  color: var(--accent-color);
}

.breadcrumb-separator {
  color: var(--text-muted);
  font-size: 12px;
}

.breadcrumb-current {
  color: var(--text-primary);
  font-weight: 500;
}

.page-header__title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-header__icon {
  color: var(--accent-color);
}

.page-header__subtitle {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 8px 0 0 0;
}

.page-header__actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Content Section */
.content-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
}

.content-section__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--secondary-bg);
}

.content-section__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-section__icon {
  color: var(--accent-color);
}

.content-section__actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.form-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 14px;
  min-width: 120px;
}

.form-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

/* Table Styles */
.table-container {
  padding: 24px;
}

.table-wrapper {
  overflow-x: auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-bg);
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.data-table th {
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tbody tr:hover {
  background: var(--hover-bg);
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}

/* Ticket Specific Styles */
.ticket-id {
  font-family: monospace;
  color: var(--text-secondary);
  font-size: 13px;
}

.ticket-subject {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-subject strong {
  color: var(--text-primary);
  font-weight: 600;
}

.user-info {
  min-width: 200px;
}

.user-name {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.user-email {
  color: var(--text-secondary);
  font-size: 13px;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-urgent {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.priority-high {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.priority-medium {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.priority-low {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-open {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-in-progress {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-resolved {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-closed {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.date-info {
  min-width: 120px;
}

.date-info .date {
  color: var(--text-primary);
  font-size: 13px;
  margin-bottom: 2px;
}

.date-info .time {
  color: var(--text-secondary);
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Button Variants */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
}

.btn--small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn--primary {
  background: var(--accent-color);
  color: #000;
}

.btn--primary:hover {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.btn--secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn--secondary:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
  background: var(--hover-bg);
}

.btn--warning {
  background: #f59e0b;
  color: white;
}

.btn--warning:hover {
  background: #d97706;
}

.btn--danger {
  background: #ef4444;
  color: white;
}

.btn--danger:hover {
  background: #dc2626;
}

/* Loading and Empty States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: var(--text-secondary);
}

.loading-state i {
  font-size: 18px;
  animation: spin 1s linear infinite;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.empty-state__icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--border-color);
}

.empty-state__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
}

.empty-state__message {
  color: var(--text-muted);
  margin: 0;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
  background: var(--secondary-bg);
}

.pagination {
  display: flex;
  gap: 4px;
  align-items: center;
}

.pagination__btn {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-secondary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination__btn:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
  background: var(--hover-bg);
}

.pagination__btn--active {
  background: var(--accent-color);
  color: #000;
  border-color: var(--accent-color);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Alert Messages */
.alert {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
}

.alert--success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.alert--error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.alert--warning {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.alert--info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Utility Classes */
.text-muted {
  color: var(--text-muted);
}

.text-success {
  color: #10b981;
}

.text-danger {
  color: #ef4444;
}

.text-warning {
  color: #f59e0b;
}

.text-info {
  color: #3b82f6;
}

/* Support Mobile Responsive */
@media (max-width: 768px) {
  .page-header__content {
    flex-direction: column;
    align-items: flex-start;
  }

  .content-section__header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .filter-group {
    flex-wrap: wrap;
    width: 100%;
  }

  .form-select {
    min-width: 100px;
    flex: 1;
  }

  .data-table {
    font-size: 13px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .btn--small {
    padding: 4px 8px;
    font-size: 11px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }

  .pagination {
    order: 2;
  }

  .pagination-info {
    order: 1;
    text-align: center;
  }
}
